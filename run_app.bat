@echo off
title Bible Quiz Competition - Local Server
color 0A
echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                 BIBLE QUIZ COMPETITION                       ║
echo  ║                    Local Server Launcher                     ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.
echo  Starting local web server for the Bible Quiz Competition app...
echo.
echo  This will:
echo  1. Start a local HTTP server on port 8000
echo  2. Automatically open the app in your default browser
echo  3. Allow all features to work properly (audio, animations, etc.)
echo.
echo  ⚠️  IMPORTANT: Keep this window open while using the app!
echo  ⚠️  Press Ctrl+C to stop the server when you're done.
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo  ✓ Python found - Starting Python HTTP server...
    echo  ✓ App will be available at: http://localhost:8000
    echo.
    echo  🌐 Opening browser in 3 seconds...
    timeout /t 3 /nobreak >nul
    start http://localhost:8000
    echo.
    echo  📡 Server is running... (Press Ctrl+C to stop)
    echo  ═══════════════════════════════════════════════════════════════
    python -m http.server 8000
) else (
    REM Check if Node.js is available
    node --version >nul 2>&1
    if %errorlevel% == 0 (
        echo  ✓ Node.js found - Starting Node.js HTTP server...
        echo  ✓ App will be available at: http://localhost:8000
        echo.
        echo  🌐 Opening browser in 3 seconds...
        timeout /t 3 /nobreak >nul
        start http://localhost:8000
        echo.
        echo  📡 Server is running... (Press Ctrl+C to stop)
        echo  ═══════════════════════════════════════════════════════════════
        npx http-server -p 8000 -c-1
    ) else (
        echo  ❌ Neither Python nor Node.js found.
        echo  📁 Opening file directly in browser...
        echo  ⚠️  Note: Some features may not work when opening files directly.
        echo.
        timeout /t 2 /nobreak >nul
        start index.html
        echo.
        echo  💡 For best experience, install Python or Node.js and run this file again.
    )
)

echo.
echo  Server stopped. Press any key to exit...
pause >nul
