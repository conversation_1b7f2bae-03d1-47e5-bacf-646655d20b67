<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bible Quiz Competition</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Header Section -->
        <header>
            <h1>Bible Quiz Competition</h1>
        </header>

        <!-- Main Menu Section -->
        <div class="menu-section" id="menuSection">
            <div class="menu-list">
                <button class="menu-button" id="goToTeamsBtn">
                    <i class="fas fa-users"></i>
                    <span>Teams</span>
                </button>
                <button class="menu-button" id="goToRoundsBtn">
                    <i class="fas fa-list-ol"></i>
                    <span>Rounds</span>
                </button>
                <button class="menu-button" id="goToScoresBtn">
                    <i class="fas fa-trophy"></i>
                    <span>View Scores</span>
                </button>
            </div>
            <!-- Floating Action Button -->
            <div class="fab-container">
                <button class="fab-button" id="fabButton">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
                <div class="fab-menu hidden" id="fabMenu">
                    <button id="goToSettingsBtn" class="fab-item"><i class="fas fa-cog"></i> <span>Settings</span></button>
                    <button id="startQuizBtn" class="fab-item"><i class="fas fa-play"></i> <span>Start Quiz</span></button>
                    <button id="continueQuizBtn" class="fab-item" style="display: none;"><i class="fas fa-forward"></i> <span>Continue</span></button>
                    <button id="exportBackupBtn" class="fab-item"><i class="fas fa-download"></i> <span>Export</span></button>
                    <button id="importBackupBtn" class="fab-item"><i class="fas fa-upload"></i> <span>Import</span></button>
                    <button id="restartQuizBtn" class="fab-item hidden"><i class="fas fa-redo"></i> <span>Restart</span></button>
                </div>
            </div>
        </div>

        <!-- Teams Section -->
        <div class="teams-section hidden">
            <div class="section-header">
                <h2>Teams</h2>
                <button id="backToMenuFromTeamsBtn" class="btn"><i class="fas fa-arrow-left"></i> Back to Menu</button>
            </div>
            <div class="teams-container" id="teamsContainer">
                <!-- Teams will be added here dynamically -->
            </div>
            <button id="addTeamBtn" class="btn"><i class="fas fa-plus"></i> Add Team</button>
        </div>

        <!-- Round Selection Section -->
        <div class="round-selection-section hidden" id="roundSelectionSection">
            <div class="section-header">
                <h2>Select a Round</h2>
                <button id="backToMenuFromRoundsBtn" class="btn"><i class="fas fa-arrow-left"></i> Back to Menu</button>
            </div>
            <div class="rounds-selection-container" id="roundsSelectionContainer">
                <!-- Round selection cards will be added here dynamically -->
            </div>
        </div>

        <!-- Quiz Section -->
        <div class="quiz-section hidden" id="quizSection">
            <div class="section-header">
                <button id="backToMenuFromQuizBtn" class="btn skyblue"><i class="fas fa-home"></i> Menu</button>
            </div>
            <div class="quiz-header">
                <div class="current-round" id="currentRoundBox">Round 1</div>
                <div class="timer-container">
                    <div class="timer-circle">
                        <div class="timer-text" id="timer">30</div>
                    </div>
                </div>
                <div class="team-score-container">
                    <div class="team-score-box">
                        <span class="team-name" id="currentTeamBox">TEAM B</span>
                        <span class="team-score-value" id="currentTeamScore">0</span>
                    </div>
                </div>
            </div>

            <div class="question-container" id="questionContainer">
                <h2 id="questionText">Loading question...</h2>
                <div class="options-container" id="optionsContainer">
                    <!-- Options will be added here dynamically -->
                </div>
            </div>

            <!-- Anagram Container (hidden by default) -->
            <div class="anagram-container hidden" id="anagramContainer">
                <h2 id="anagramInstructions">Complete the word by clicking the letters in the correct order</h2>

                <!-- Word Display with Letter Boxes -->
                <div class="anagram-word-display" id="anagramWordDisplay">
                    <!-- Letter boxes will be generated dynamically -->
                </div>

                <!-- Random Letters Area -->
                <div class="anagram-letters-section">
                    <h3>Available Letters:</h3>
                    <div class="anagram-random-letters" id="anagramRandomLettersDisplay">
                        <!-- Random letters will be generated dynamically -->
                    </div>
                </div>
            </div>

            <div style="text-align: center;">
                <div class="question-counter" id="questionCounter">Question <span id="currentQuestion">1</span> of <span id="totalQuestions">10</span></div>
                <div class="anagram-counter hidden" id="anagramCounter">Anagram <span id="currentAnagram">1</span> of <span id="totalAnagrams">1</span></div>
            </div>

            <div class="quiz-controls">
                <button id="startTimerBtn" class="btn skyblue"><i class="fas fa-play"></i> Start Timer</button>
                <button id="nextQuestionBtn" class="btn skyblue" disabled><i class="fas fa-arrow-right"></i> Next Question</button>
            </div>
        </div>

        <!-- Scores Section -->
        <div class="scores-section hidden" id="scoresSection">
            <div class="section-header">
                <h2>Current Scores</h2>
                <button id="backToMenuFromScoresBtn" class="btn"><i class="fas fa-arrow-left"></i> Back to Menu</button>
            </div>

            <div class="scores-main-content">
                <div class="scores-leaderboard" id="scoresLeaderboard">
                    <!-- Leaderboard will be added here dynamically -->
                </div>

                <div class="scores-controls">
                    <button id="updateScoresBtn" class="btn update-btn">
                        <i class="fas fa-sync-alt"></i> Update Scores
                    </button>
                </div>
            </div>

            <div class="scores-summary">
                <h3>Rounds Completed</h3>
                <div class="rounds-summary" id="roundsSummary">
                    <!-- Rounds summary will be added here dynamically -->
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="results-section hidden" id="resultsSection">
            <h2>Round Results</h2>
            <div class="results-container" id="resultsContainer">
                <!-- Results will be added here dynamically -->
            </div>
            <div class="results-controls">
                <button id="selectNextRoundBtn" class="btn skyblue"><i class="fas fa-list"></i> Select Next Round</button>
            </div>
            <div class="results-actions">
                <button id="newQuizBtn" class="btn skyblue"><i class="fas fa-redo"></i> New Quiz</button>
            </div>
        </div>

        <!-- Settings Modal -->
        <div class="modal" id="settingsModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Settings</h2>
                    <span class="close-btn" id="closeSettingsBtn">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="settings-tabs">
                        <button class="tab-btn" data-tab="rounds">Rounds</button>
                        <button class="tab-btn active" data-tab="questions">Questions</button>
                        <button class="tab-btn" data-tab="teams">Teams</button>
                        <button class="tab-btn" data-tab="general">General</button>
                    </div>

                    <div class="tab-content" id="roundsTab">
                        <div class="rounds-list" id="roundsList">
                            <!-- Rounds will be added here dynamically -->
                        </div>
                        <button id="addRoundBtn" class="btn"><i class="fas fa-plus"></i> Add Round</button>
                    </div>

                    <div class="tab-content active" id="questionsTab">
                        <div class="round-selector">
                            <label for="questionRoundSelect">Add to Round:</label>
                            <select id="questionRoundSelect">
                                <!-- Round options will be added here dynamically -->
                            </select>
                        </div>
                        <div class="questions-list" id="questionsList">
                            <!-- Questions will be added here dynamically -->
                        </div>
                        <button id="addQuestionBtn" class="btn"><i class="fas fa-plus"></i> Add Question</button>
                        <button id="addAnagramBtn" class="btn"><i class="fas fa-puzzle-piece"></i> Add Anagram</button>
                    </div>

                    <div class="tab-content" id="teamsTab">
                        <div class="settings-teams-list" id="settingsTeamsList">
                            <!-- Teams will be added here dynamically -->
                        </div>
                        <button id="settingsAddTeamBtn" class="btn"><i class="fas fa-plus"></i> Add Team</button>
                    </div>

                    <div class="tab-content" id="generalTab">
                        <div class="setting-item">
                            <label for="quizTitle">Quiz Title:</label>
                            <input type="text" id="quizTitle" placeholder="Enter quiz title" value="Bible Quiz Competition">
                        </div>
                        <div class="setting-item">
                            <label for="timerDuration">Timer Duration (seconds):</label>
                            <input type="number" id="timerDuration" min="5" max="60" value="30">
                        </div>
                        <div class="setting-item">
                            <label for="pointsPerCorrectAnswer">Points Per Correct Answer:</label>
                            <input type="number" id="pointsPerCorrectAnswer" min="1" max="1000" value="1">
                        </div>
                        <div class="setting-item">
                            <label for="shuffleQuestions">Shuffle Questions:</label>
                            <input type="checkbox" id="shuffleQuestions" checked>
                        </div>
                        <div class="setting-item">
                            <label for="titleAlignment">Title Alignment:</label>
                            <select id="titleAlignment">
                                <option value="left">Left</option>
                                <option value="center">Center</option>
                                <option value="right">Right</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label for="titleSize">Title Size:</label>
                            <div class="slider-container">
                                <input type="range" id="titleSize" min="1" max="3" step="0.1" value="2">
                                <div class="slider-value">Medium</div>
                            </div>
                        </div>
                        <div class="setting-item">
                            <label for="questionAlignment">Question Text Alignment:</label>
                            <select id="questionAlignment">
                                <option value="left">Left</option>
                                <option value="center">Center</option>
                                <option value="right">Right</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label for="questionSize">Question Text Size:</label>
                            <div class="slider-container">
                                <input type="range" id="questionSize" min="1" max="3" step="0.1" value="1.5">
                                <div class="slider-value-question">Medium</div>
                            </div>
                        </div>
                        <div class="setting-item">
                            <label for="timerSize">Timer Size:</label>
                            <div class="slider-container">
                                <input type="range" id="timerSize" min="0.7" max="1.3" step="0.1" value="1">
                                <div class="slider-value-timer">Medium</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="saveSettingsBtn" class="btn skyblue">Save Settings</button>
                </div>
            </div>
        </div>

        <!-- Add Question Modal -->
        <div class="modal" id="addQuestionModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="questionModalTitle">Add New Question</h2>
                    <span class="close-btn" id="closeQuestionModalBtn">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="questionImageSelect">Question Image (Optional):</label>
                        <div class="image-selection-container">
                            <select id="questionImageSelect">
                                <option value="">No Image</option>
                                <!-- Image options will be populated dynamically -->
                            </select>
                            <div id="questionImagePreview" class="image-preview"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="questionInput">Question:</label>
                        <textarea id="questionInput" rows="3" placeholder="Enter your question here"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Options:</label>
                        <div class="option-input">
                            <input type="radio" name="correctOption" id="option1Radio" value="0" checked>
                            <input type="text" id="option1Input" placeholder="Option 1">
                        </div>
                        <div class="option-input">
                            <input type="radio" name="correctOption" id="option2Radio" value="1">
                            <input type="text" id="option2Input" placeholder="Option 2">
                        </div>
                        <div class="option-input">
                            <input type="radio" name="correctOption" id="option3Radio" value="2">
                            <input type="text" id="option3Input" placeholder="Option 3">
                        </div>
                        <div class="option-input">
                            <input type="radio" name="correctOption" id="option4Radio" value="3">
                            <input type="text" id="option4Input" placeholder="Option 4">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="saveQuestionBtn" class="btn skyblue">Save Question</button>
                </div>
            </div>
        </div>

        <!-- Add Team Modal -->
        <div class="modal" id="addTeamModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Add New Team</h2>
                    <span class="close-btn" id="closeTeamModalBtn">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="teamNameInput">Team Name:</label>
                        <input type="text" id="teamNameInput" placeholder="Enter team name">
                    </div>
                    <div class="form-group">
                        <label for="teamBackgroundColorInput">Card Background (CSS color or gradient):</label>
                        <input type="text" id="teamBackgroundColorInput" placeholder="e.g., #f9f9f9 or linear-gradient(to right, red, blue)">
                    </div>
                    <div class="form-group">
                        <label>Participants:</label>
                        <div id="participantsContainer">
                            <div class="participant-input">
                                <div class="participant-name-container">
                                    <select class="participant-name-select">
                                        <option value="">Select a name...</option>
                                    </select>
                                    <input type="text" class="participant-name-input" placeholder="Enter new name" style="display: none;">
                                    <button class="add-name-btn" style="display: none;"><i class="fas fa-check"></i></button>
                                    <button class="cancel-name-btn" style="display: none;"><i class="fas fa-times"></i></button>
                                </div>
                                <select class="participant-profile-image">
                                    <option value="">No Image</option>
                                </select>
                                <button class="remove-participant-btn"><i class="fas fa-times"></i></button>
                            </div>
                        </div>
                        <button id="addParticipantBtn" class="btn small"><i class="fas fa-plus"></i> Add Participant</button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="saveTeamBtn" class="btn skyblue">Save Team</button>
                </div>
            </div>
        </div>

        <!-- Add Round Modal -->
        <div class="modal" id="addRoundModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="roundModalTitle">Add New Round</h2>
                    <span class="close-btn" id="closeRoundModalBtn">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="roundNameInput">Round Name:</label>
                        <input type="text" id="roundNameInput" placeholder="Enter round name">
                    </div>
                    <div class="form-group">
                        <label for="roundTypeSelect">Round Type:</label>
                        <select id="roundTypeSelect">
                            <option value="questions">Question Round</option>
                            <option value="anagrams">Anagram Round</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="saveRoundBtn" class="btn skyblue">Save Round</button>
                </div>
            </div>
        </div>

        <!-- Add Anagram Modal -->
        <div class="modal" id="addAnagramModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="anagramModalTitle">Add New Anagram</h2>
                    <span class="close-btn" id="closeAnagramModalBtn">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="anagramWordInput">Word:</label>
                        <input type="text" id="anagramWordInput" placeholder="Enter the complete word (e.g., DEKAPOLIS)" style="text-transform: uppercase;">
                    </div>
                    <div class="form-group">
                        <label for="anagramHintLetters">Hint Letters (Position:Letter):</label>
                        <div class="hint-letters-container" id="hintLettersContainer">
                            <div class="hint-letter-input">
                                <input type="number" placeholder="Position" min="1" class="hint-position">
                                <input type="text" placeholder="Letter" maxlength="1" class="hint-letter" style="text-transform: uppercase;">
                                <button type="button" class="remove-hint-btn"><i class="fas fa-times"></i></button>
                            </div>
                        </div>
                        <button type="button" id="addHintLetterBtn" class="btn small"><i class="fas fa-plus"></i> Add Hint Letter</button>
                    </div>
                    <div class="form-group">
                        <label for="anagramRandomLetters">Random Letters:</label>
                        <input type="text" id="anagramRandomLetters" placeholder="Enter letters separated by commas (e.g., D, K, P, A, I)" style="text-transform: uppercase;">
                        <small>These are the letters that participants will click to complete the word. Enter each letter separated by commas.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="saveAnagramBtn" class="btn skyblue">Save Anagram</button>
                </div>
            </div>
        </div>

        <!-- Team Introduction Modal -->
        <div class="team-introduction-modal" id="teamIntroductionModal" role="dialog" aria-modal="true" aria-labelledby="teamIntroName" aria-hidden="true">
            <div class="team-intro-overlay"></div>
            <div class="team-intro-content">
                <button class="team-intro-close-btn" id="teamIntroCloseBtn" aria-label="Close team introduction" tabindex="0">
                    <i class="fas fa-times"></i>
                </button>

                <div class="team-intro-header">
                    <h1 class="team-intro-name" id="teamIntroName" tabindex="0">Team Name</h1>
                </div>

                <div class="team-intro-members" id="teamIntroMembers" role="list" aria-label="Team members">
                    <!-- Team members will be populated dynamically -->
                </div>
            </div>
        </div>

        <!-- Image Preview Modal -->
        <div class="image-preview-modal" id="imagePreviewModal" role="dialog" aria-modal="true" aria-hidden="true">
            <div class="image-preview-overlay" id="imagePreviewOverlay"></div>
            <div class="image-preview-content">
                <button class="image-preview-close-btn" id="imagePreviewCloseBtn" aria-label="Close image preview" tabindex="0">
                    <i class="fas fa-times"></i>
                </button>
                <div class="image-preview-container">
                    <img id="imagePreviewImg" src="" alt="Question Image" />
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden file input for importing backups -->
    <input type="file" id="backupFileInput" accept=".json" style="display: none;">

    <script src="script.js"></script>
</body>
</html>