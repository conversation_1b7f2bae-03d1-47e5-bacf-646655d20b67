/* Global Styles */
:root {
    --primary-color: #2e3192; /* Deep blue */
    --secondary-color: #ffaa65; /* Bright orange */
    --accent-color: #ff7300; /* Green */
    --dark-color: #1a1a2e; /* Dark blue */
    --light-color: #f5f5f7; /* Light gray */
    --correct-color: #00a651; /* Green */
    --incorrect-color: #e63946; /* Red */
    --neutral-color: #457b9d; /* Blue-gray */
    --shadow-color: rgba(0, 0, 0, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Montserrat', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--dark-color);
    background-image:
        radial-gradient(circle at 20% 30%, rgba(46, 49, 146, 0.7) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(247, 148, 29, 0.5) 0%, transparent 50%),
        url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M50 0 L100 50 L50 100 L0 50 Z" fill="rgba(255,255,255,0.03)"/></svg>');
    background-size: 200% 200%, 200% 200%, 100px 100px;
    animation: backgroundShift 20s ease infinite;
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: #333;
    position: relative;
    overflow-x: hidden;
}

@keyframes backgroundShift {
    0% { background-position: 0% 0%, 100% 100%, 0 0; }
    50% { background-position: 100% 100%, 0% 0%, 25px 25px; }
    100% { background-position: 0% 0%, 100% 100%, 0 0; }
}

.app-container {
    background-color: var(--light-color);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 1600px;
    min-height: 80vh;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 30px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Header Styles */
header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(46, 49, 146, 0.2);
    position: relative;
}

header::before, header::after {
    content: '';
    position: absolute;
    width: 100px;
    height: 100px;
    background-image: url('data:image/svg+xml;utf8,<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 0 L14 8 L22 8 L16 13 L18 22 L12 17 L6 22 L8 13 L2 8 L10 8 Z" fill="rgba(247,148,29,0.2)"/></svg>');
    background-repeat: no-repeat;
    background-size: contain;
    opacity: 0.5;
    z-index: 0;
}

header::before {
    left: 10%;
    top: 0;
    transform: rotate(-15deg);
}

header::after {
    right: 10%;
    top: 0;
    transform: rotate(15deg);
}

header h1 {
    color: var(--primary-color);
    font-size: 3rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 3px 3px 0 rgba(0, 0, 0, 0.15);
    position: relative;
    z-index: 1;
    background: linear-gradient(45deg, #1a237e, #ff6600);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding: 10px 0;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Celebration Animation */
.celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-in-out;
    overflow: hidden;
}

.celebration-popup {
    background-color: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 60px rgba(40, 167, 69, 0.5);
    max-width: 500px;
    width: 90%;
    position: relative;
    overflow: hidden;
    animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 3px solid #28a745;
}

.celebration-popup::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle at center, rgba(40, 167, 69, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
    z-index: -1;
    animation: pulse-glow 2s ease-in-out infinite;
}

.celebration-popup h2 {
    color: #28a745;
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    display: inline-block;
}

.celebration-popup h2::before {
    content: '✓';
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

.celebration-popup p {
    font-size: 1.5rem;
    margin-bottom: 30px;
    color: #333;
}


.celebration-popup .points-awarded {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 30px;
    display: inline-block;
    margin-bottom: 20px;
    border: 1px solid rgba(40, 167, 69, 0.3);
    animation: pulse-scale 1.5s ease-in-out infinite;
}

.celebration-popup .btn.skyblue {
    background: linear-gradient(to right, #45b7d8, #2196f3);
    color: white;
    padding: 10px 25px;
    font-weight: bold;
    border-radius: 30px;
    border: none;
    box-shadow: 0 4px 10px rgba(33, 150, 243, 0.3);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.celebration-popup .btn.skyblue:hover {
    background: linear-gradient(to right, #45b7d8, #1e88e5);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(33, 150, 243, 0.4);
}

.celebration-confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #ffcc00;
    border-radius: 50%;
    animation: confetti 3s ease-in-out infinite;
}

.celebration-star {
    position: absolute;
    width: 0;
    height: 0;
    border-right: 10px solid transparent;
    border-bottom: 7px solid #ffcc00;
    border-left: 10px solid transparent;
    transform: rotate(35deg);
    animation: star-fall 2s ease-in-out infinite;
}

.celebration-star:before {
    border-bottom: 8px solid #ffcc00;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    position: absolute;
    height: 0;
    width: 0;
    top: -5px;
    left: -6px;
    content: '';
    transform: rotate(-35deg);
}

.celebration-star:after {
    position: absolute;
    top: 0;
    left: -10px;
    border-right: 10px solid transparent;
    border-bottom: 7px solid #ffcc00;
    border-left: 10px solid transparent;
    transform: rotate(-70deg);
    content: '';
}

.celebration-firework {
    position: absolute;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    animation: firework 1s ease-out forwards;
    transform-origin: center;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes popIn {
    0% { transform: scale(0.5); opacity: 0; }
    70% { transform: scale(1.05); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes confetti {
    0% { transform: translateY(-100px) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
}

@keyframes star-fall {
    0% { transform: translateY(-100px) rotate(35deg) scale(0); opacity: 1; }
    50% { opacity: 1; transform: translateY(50vh) rotate(35deg) scale(1); }
    100% { transform: translateY(100vh) rotate(35deg) scale(0.5); opacity: 0; }
}

@keyframes firework {
    0% { transform: translate(0, 0) scale(0.1); opacity: 1; box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5); }
    50% { opacity: 1; }
    100% { transform: translate(var(--x), var(--y)) scale(1); opacity: 0; box-shadow: 0 0 20px 5px rgba(255, 255, 255, 0); }
}

@keyframes pulse-glow {
    0% { opacity: 0.5; }
    50% { opacity: 1; }
    100% { opacity: 0.5; }
}

@keyframes pulse-scale {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Control Buttons Outside Container */
.control-buttons {
    position: fixed;
    bottom: 20px;
    left: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 100;
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    background: linear-gradient(to right, var(--primary-color), var(--neutral-color));
    color: white;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15), inset 0 -3px 0 rgba(0, 0, 0, 0.2);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2), inset 0 -3px 0 rgba(0, 0, 0, 0.3);
}

.btn:hover::after {
    left: 100%;
}

.btn.primary {
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2), inset 0 -3px 0 rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn.primary:hover {
    background: linear-gradient(to right, var(--primary-color), #f7941d);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25), inset 0 -3px 0 rgba(0, 0, 0, 0.3);
}

.btn.skyblue {
    background: linear-gradient(to right, #45b7d8, #2196f3);
    color: white;
    box-shadow: 0 6px 15px rgba(33, 150, 243, 0.3), inset 0 -3px 0 rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn.skyblue:hover {
    background: linear-gradient(to right, #45b7d8, #1e88e5);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(33, 150, 243, 0.4), inset 0 -3px 0 rgba(0, 0, 0, 0.2);
}

.btn.small {
    padding: 5px 10px;
    font-size: 0.9rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Menu Section */
.menu-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 30px 0 80px 0; /* Added bottom padding for the buttons */
    margin-bottom: 30px;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    min-height: 400px;
}

@media (max-width: 767px) {
    .menu-section {
        padding: 20px 0 20px 0;
        min-height: auto;
        margin-bottom: 0;
    }
}

.menu-title {
    color: #1a2a6c;
    font-size: 2rem;
    margin-bottom: 30px;
    text-align: center;
}

.menu-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 15px;
    margin-bottom: 20px;
}

@media (max-width: 767px) {
    .menu-list {
        margin-bottom: 10px;
        gap: 10px;
    }
}

.menu-button {
    display: flex;
    align-items: center;
    padding: 18px 25px;
    background: linear-gradient(135deg, var(--light-color), #e6e6e6);
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1), inset 0 -4px 0 rgba(0, 0, 0, 0.05);
    text-align: left;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.menu-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    z-index: -1;
}

.menu-button::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 100%;
    top: 0;
    left: -100px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: 0.5s;
    z-index: 1;
}

.menu-button:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15), inset 0 -4px 0 rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
}

.menu-button:hover::after {
    left: 150%;
}

.menu-button i {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-right: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(46, 49, 146, 0.1);
    border-radius: 50%;
    padding: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.menu-button span {
    font-size: 1.5rem;
    font-weight: 800;
    color: #1a237e;
    flex-grow: 1;
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
    letter-spacing: 0.5px;
}

.menu-button.primary {
    background: linear-gradient(135deg, #f7941d, #f7941d);
    box-shadow: 0 6px 15px rgba(247, 148, 29, 0.3), inset 0 -3px 0 rgba(0, 0, 0, 0.1);
}

.menu-button.primary i {
    color: white;
    background: rgba(255, 255, 255, 0.2);
}

.menu-button.primary span {
    color: white;
    text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.2);
}

.menu-button.primary:hover {
    background: linear-gradient(135deg, #f7941d, #ff9f2a);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(247, 148, 29, 0.4), inset 0 -3px 0 rgba(0, 0, 0, 0.2);
}

/* Floating Action Button */
.fab-container {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 999;
}

.fab-button {
    width: 65px;
    height: 65px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: white;
    border: 3px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), inset 0 2px 5px rgba(255, 255, 255, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 1.6rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 1000;
    position: relative;
    overflow: hidden;
}

.fab-button::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.4) 0%, transparent 60%);
    opacity: 0.6;
}

.fab-button:hover {
    transform: scale(1.15) rotate(5deg);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.4), inset 0 2px 5px rgba(255, 255, 255, 0.4);
}

.fab-button.active {
    transform: rotate(135deg);
    background: linear-gradient(135deg, var(--incorrect-color), #c62828);
    border-color: rgba(255, 255, 255, 0.9);
}

.fab-menu {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column-reverse;
    align-items: flex-end;
    gap: 12px;
    transition: all 0.3s ease;
    opacity: 1;
    transform: translateY(0);
    z-index: 999;
    width: 220px; /* Fixed width for consistent alignment */
}

.fab-menu.hidden {
    opacity: 0;
    transform: translateY(20px);
    pointer-events: none;
}

.fab-item {
    background: linear-gradient(135deg, #ffffff, #f5f5f5);
    color: var(--primary-color);
    border: 2px solid rgba(46, 49, 146, 0.2);
    border-radius: 30px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    white-space: nowrap;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), inset 0 -2px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation: fadeIn 0.4s ease forwards;
    font-weight: 600;
    letter-spacing: 0.5px;
    position: relative;
    overflow: visible; /* Changed from hidden to visible */
    width: 100%;
    height: 48px;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.fab-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), transparent);
    z-index: -1;
}

.fab-item i {
    margin-right: 15px;
    font-size: 1.1rem;
    color: var(--secondary-color);
    background: rgba(247, 148, 29, 0.1);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid rgba(247, 148, 29, 0.3);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.fab-item:hover {
    background: linear-gradient(135deg, #f0f0f0, #e8e8e8);
    transform: translateX(-8px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), inset 0 -2px 0 rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.fab-item:hover i {
    background: rgba(247, 148, 29, 0.2);
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Add animation delay for each fab-item to create a staggered effect */
.fab-menu .fab-item:nth-child(1) {
    animation-delay: 0.05s;
}
.fab-menu .fab-item:nth-child(2) {
    animation-delay: 0.1s;
}
.fab-menu .fab-item:nth-child(3) {
    animation-delay: 0.15s;
}
.fab-menu .fab-item:nth-child(4) {
    animation-delay: 0.2s;
}
.fab-menu .fab-item:nth-child(5) {
    animation-delay: 0.25s;
}
.fab-menu .fab-item:nth-child(6) {
    animation-delay: 0.3s;
}

/* Specific styles for fab-item buttons to ensure text is visible */
.fab-item {
    display: flex !important;
    align-items: center;
    justify-content: flex-start;
    white-space: nowrap;
    overflow: visible;
}

.fab-item span {
    display: inline-block;
    white-space: nowrap;
    flex: 1;
}

/* Mobile-specific styles */
@media (max-width: 767px) {
    .fab-container {
        bottom: 20px;
        right: 20px;
    }

    .fab-menu {
        width: 180px;
        bottom: 65px;
    }

    .fab-item {
        font-size: 0.8rem;
        padding: 10px 15px;
        height: 42px;
    }

    .fab-item i {
        width: 28px;
        height: 28px;
        margin-right: 10px;
        font-size: 0.9rem;
    }
}

.btn-sm {
    padding: 6px 8px;
    font-size: 0.75rem;
    border-radius: 4px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    transition: all 0.2s ease;
    height: 30px;
}

.btn-sm:hover {
    background-color: #e9e9e9;
}

.menu-actions .btn i {
    margin-right: 5px;
    width: 14px;
    text-align: center;
    font-size: 0.8rem;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
}

/* Teams Section */
.teams-section {
    margin-bottom: 30px;
}

.teams-section h2 {
    margin-bottom: 15px;
    color: #1a2a6c;
}

.teams-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
}

.team-card {
    background: linear-gradient(145deg, #ffffff, #f0f0f0);
    border-radius: 15px;
    padding: 25px;
    width: calc(25% - 15px);
    min-width: 280px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.5);
    cursor: pointer;
}

.team-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(46, 49, 146, 0.05), transparent);
    z-index: 0;
}

.team-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.08);
}

.team-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.team-card h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 800;
    text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.05);
}

.team-actions {
    display: flex;
    gap: 8px;
}

.edit-team-btn, .delete-team-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    color: #777;
    transition: color 0.3s ease, transform 0.2s ease;
    padding: 5px;
    border-radius: 5px;
}

.edit-team-btn:hover {
    color: var(--primary-color);
    transform: scale(1.1);
}

.delete-team-btn:hover {
    color: var(--incorrect-color);
    transform: scale(1.1);
}

.participants-list {
    list-style: none;
    padding: 0;
    margin-top: 15px;
    width: 100%;
    position: relative;
    z-index: 1;
}

.participants-list li {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-top: 1px solid #eee;
    color: #555;
    font-size: 1.1rem;
    font-weight: 600;
}

.participants-list li:first-child {
    border-top: none;
}

.participant-profile-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 15px;
    border: 3px solid var(--secondary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.participant-name {
    flex-grow: 1;
    text-align: left;
}

/* Team Introduction Modal Styles */
.team-introduction-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.team-introduction-modal.active {
    display: flex;
    opacity: 1;
}

.team-intro-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(26, 42, 108, 0.95), rgba(46, 49, 146, 0.95));
    backdrop-filter: blur(10px);
}

.team-intro-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px;
    overflow-y: auto;
}

.team-intro-close-btn {
    position: absolute;
    top: 30px;
    right: 30px;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    z-index: 10;
}

.team-intro-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

.team-intro-header {
    text-align: center;
    margin-bottom: 60px;
}

.team-intro-name {
    font-size: 4rem;
    font-weight: 900;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin: 0;
    opacity: 0;
    padding-bottom: 100px;
    transform: scale(0.5);
    animation: teamNameEntrance 1s ease-out 0.5s forwards;
}

@keyframes teamNameEntrance {
    0% {
        opacity: 0;
        transform: scale(0.5) rotateY(-90deg);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1) rotateY(0deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotateY(0deg);
    }
}

.team-intro-members {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    max-width: 1200px;
    width: 100%;
}

.team-member-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px 30px 40px 30px;
    text-align: center;
    opacity: 0;
    transform: translateY(50px) scale(0.8);
    transition: all 0.3s ease;
    position: relative;
    margin-top: 80px; /* Space for overlaying profile image */
    min-height: 120px;
}

.team-member-card.animate-in {
    animation: memberCardEntrance 0.8s ease-out forwards;
}

@keyframes memberCardEntrance {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.8) rotateX(-15deg);
    }
    60% {
        opacity: 0.8;
        transform: translateY(-10px) scale(1.05) rotateX(5deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg);
    }
}

.team-member-card:hover {
    transform: translateY(-10px) scale(1.05);
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
}

.team-member-card:hover .member-profile-img {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.member-profile-container {
    position: absolute;
    top: -160px; /* Position to overlay on top of the card */
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.member-profile-img {
    width: 260px;
    height: 260px;
    border-radius: 15px; /* Square/rectangular with rounded corners or to Make it round */
    border: 4px solid rgba(255, 255, 255, 0.8);
    object-fit: cover;
    display: block;
    transition: all 0.3s ease;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.member-name {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    margin: 80px 0 0 0; /* Top margin to account for overlaying image */
    opacity: 0;
    transform: translateY(20px);
    padding-top: 10px;
}

.member-name.animate-in {
    animation: memberNameEntrance 0.6s ease-out forwards;
}

@keyframes memberNameEntrance {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments for team cards */
@media (max-width: 1200px) {
    .team-card {
        width: calc(33.33% - 15px);
        min-width: 280px;
    }

    .team-intro-name {
        font-size: 3rem;
    }

    .team-intro-members {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
    }
}

@media (max-width: 900px) {
    .team-card {
        width: calc(50% - 15px);
        min-width: 280px;
    }

    .team-intro-content {
        padding: 20px;
    }

    .team-intro-name {
        font-size: 2.5rem;
    }

    .team-intro-members {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .team-intro-close-btn {
        top: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .member-profile-img {
        width: 150px;
        height: 150px;
    }

    .member-name {
        margin-top: 75px;
    }

    .team-member-card {
        margin-top: 75px;
    }
}

@media (max-width: 600px) {
    .team-card {
        width: 100%;
        min-width: unset;
    }

    .team-intro-name {
        font-size: 2rem;
    }

    .member-profile-img {
        width: 140px;
        height: 140px;
        border-radius: 12px;
    }

    .member-name {
        font-size: 1.3rem;
        margin-top: 70px;
    }

    .team-member-card {
        padding: 20px 20px 30px 20px;
        margin-top: 70px;
        min-height: 100px;
    }

    .member-profile-container {
        top: -50px;
    }
}

/* Quiz Section */
.quiz-section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.quiz-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
    position: relative;
    min-height: 200px;
    padding-top: 20px;
    background: linear-gradient(135deg, rgba(46, 49, 146, 0.1), rgba(247, 148, 29, 0.1));
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 25px;
    overflow: hidden;
}

.quiz-header::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
    background-size: 30px 30px;
    opacity: 0.5;
    z-index: 0;
}

.question-counter {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin: 20px auto;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.5));
    padding: 10px 20px;
    border-radius: 50px;
    display: inline-block;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    border: 1px solid rgba(46, 49, 146, 0.2);
}

/* Common styles for both team and round boxes */
.current-team, .current-round {
    font-size: 0.9rem;
    font-weight: bold;
    color: #fd702f;
    text-align: center;
    position: relative;
    z-index: 5;
    width: auto;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: white;
    padding: 5px 10px;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Team score container */
.team-score-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    order: 3;
}

/* Team score box */
.team-score-box {
    display: flex;
    align-items: center;
    background: white;
    padding: 5px 10px;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Team name */
.team-name {
    font-size: 1.2rem;
    font-weight: bold;
    color: #fd702f; /* Orange color */
    margin-right: 5px;
}

/* Team score value */
.team-score-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #00a651; /* Green color */
}

/* Specific positioning for round */
.current-round {
    margin-bottom: 15px;
    order: 1;
    border: 0px solid #7986cb; /* Blue border for round box */
}

.timer-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    margin: 20px 0;
    order: 2;
}

/* Media query for larger screens */
@media (min-width: 768px) {
    .quiz-header {
        flex-direction: row;
        justify-content: space-between;
        min-height: 150px;
        padding-top: 0;
    }

    .current-round {
        margin: 0;
        order: unset;
        font-size: 1.5rem;
    }

    .team-score-container {
        order: unset;
        align-items: flex-end;
    }

    .team-score-box {
        padding: 8px 15px;
    }

    .team-name {
        font-size: 1.5rem;
        margin-right: 10px;
    }

    .team-score-value {
        font-size: 1.5rem;
    }

    .current-round {
        text-align: left;
    }

    .timer-container {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        margin: 0;
        order: unset;
    }
}

.timer-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), inset 0 2px 5px rgba(255, 255, 255, 0.5), inset 0 -2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin: 0 auto;
    border: 4px solid white;
}

@media (min-width: 768px) {
    .timer-circle {
        width: 120px;
        height: 120px;
    }
}

.timer-circle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: conic-gradient(var(--primary-color) var(--progress, 100%), transparent 0);
    mask: radial-gradient(transparent 55%, black 56%);
    -webkit-mask: radial-gradient(transparent 55%, black 56%);
    opacity: 0.8;
}

.timer-circle::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 60%);
    opacity: 0.3;
}

.timer-circle.paused {
    box-shadow: 0 0 0 3px #dc3545, 0 4px 8px rgba(0, 0, 0, 0.1);
    animation: pulse 1.5s infinite;
}

.timer-circle.paused::after {
    content: 'PAUSED';
    position: absolute;
    bottom: 10px;
    font-size: 0.7rem;
    font-weight: bold;
    color: #dc3545;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7), 0 4px 8px rgba(0, 0, 0, 0.1); }
    70% { box-shadow: 0 0 0 6px rgba(220, 53, 69, 0), 0 4px 8px rgba(0, 0, 0, 0.1); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0), 0 4px 8px rgba(0, 0, 0, 0.1); }
}

.timer-text {
    font-size: 2.2rem;
    font-weight: 800;
    color: var(--primary-color);
    z-index: 1;
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
    font-family: 'Digital-7', 'Orbitron', monospace, sans-serif;
    letter-spacing: 1px;
}

@media (min-width: 768px) {
    .timer-text {
        font-size: 2.8rem;
    }
}

.question-container {
    background: linear-gradient(135deg, var(--light-color), #e6e6e6);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
    clear: both;
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
}

.question-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    z-index: -1;
}

.question-container h2 {
    margin-bottom: 25px;
    color: #000000;
    font-size: 2rem;
    line-height: 1.5;
    text-align: center;
    font-weight: 800;
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    letter-spacing: 0.5px;
}

@media (min-width: 768px) {
    .question-container {
        padding: 30px;
    }

    .question-container h2 {
        font-size: 1.8rem;
    }
}

.options-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

@media (min-width: 768px) {
    .options-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

.option {
    background: linear-gradient(135deg, #ffffff, #f5f5f5);
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    font-size: 1.2rem;
    text-align: center;
    font-weight: 700;
    color: #1a237e;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 0.3px;
}

.option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), transparent);
    z-index: 1;
    opacity: 0.8;
}

.option span {
    position: relative;
    z-index: 2;
}

@media (min-width: 768px) {
    .option {
        padding: 18px 25px;
        font-size: 1.3rem;
        text-align: center;
    }
}

.option:hover {
    transform: translateY(-5px) scale(1.02);
    border-color: var(--primary-color);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1), inset 0 -3px 0 rgba(0, 0, 0, 0.1);
}

.option.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(46, 49, 146, 0.1), rgba(46, 49, 146, 0.05));
    box-shadow: 0 4px 10px rgba(46, 49, 146, 0.2), inset 0 -3px 0 rgba(46, 49, 146, 0.1);
}

.option.correct {
    border-color: #00a651;
    background: linear-gradient(135deg, rgba(0, 166, 81, 0.3), rgba(0, 166, 81, 0.2));
    box-shadow: 0 4px 10px rgba(0, 166, 81, 0.4), inset 0 -3px 0 rgba(0, 166, 81, 0.2);
    color: #006633;
    font-weight: 800;
    animation: correctPulse 1s infinite;
}

@keyframes correctPulse {
    0% { box-shadow: 0 4px 10px rgba(0, 166, 81, 0.3), inset 0 -3px 0 rgba(0, 166, 81, 0.1); }
    50% { box-shadow: 0 4px 20px rgba(0, 166, 81, 0.5), inset 0 -3px 0 rgba(0, 166, 81, 0.2); }
    100% { box-shadow: 0 4px 10px rgba(0, 166, 81, 0.3), inset 0 -3px 0 rgba(0, 166, 81, 0.1); }
}

.option.incorrect {
    border-color: #e63946;
    background: linear-gradient(135deg, rgba(230, 57, 70, 0.3), rgba(230, 57, 70, 0.2));
    box-shadow: 0 4px 10px rgba(230, 57, 70, 0.4), inset 0 -3px 0 rgba(230, 57, 70, 0.2);
    color: #b71c1c;
    font-weight: 800;
}

.question-counter {
    font-size: 1.1rem;
    color: #333333;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.quiz-controls {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 20px;
}

.quiz-controls .btn {
    min-width: 180px;
    padding: 12px 25px;
    font-size: 1.1rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-radius: 30px;
}

.quiz-controls .btn.primary, .quiz-controls .btn.skyblue {
    background: linear-gradient(to right, #45b7d8, #2196f3);
    border: none;
    box-shadow: 0 4px 10px rgba(33, 150, 243, 0.4);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.quiz-controls .btn.primary:hover, .quiz-controls .btn.skyblue:hover {
    background: linear-gradient(to right, #45b7d8, #1e88e5);
    box-shadow: 0 6px 15px rgba(33, 150, 243, 0.5);
    transform: translateY(-2px);
}

.quiz-controls .btn::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 60%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.quiz-controls .btn:hover::after {
    opacity: 1;
}

/* Round Selection Section */
.round-selection-section {
    flex: 1;
}

.round-selection-section h2 {
    margin-bottom: 20px;
    color: #1a2a6c;
}

.rounds-selection-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.round-card {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 20px;
    width: calc(33.33% - 15px);
    min-width: 250px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.round-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.round-card.completed {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
}

.round-card.completed::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    color: #4caf50;
    font-size: 1.5rem;
    font-weight: bold;
}

.round-card.in-progress {
    background-color: #e3f2fd;
    border: 2px solid #2196f3;
}

.round-card.in-progress::after {
    content: '▶';
    position: absolute;
    top: 10px;
    right: 10px;
    color: #2196f3;
    font-size: 1.5rem;
    font-weight: bold;
}

.in-progress-text {
    color: #2196f3;
    font-weight: bold;
    margin-top: 5px;
}

.progress-text {
    color: #2196f3;
    font-style: italic;
    margin-top: 5px;
}

.round-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #1a2a6c;
}

.round-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.round-selection-controls {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

/* Scores Section - Redesigned */
.scores-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding-top: 20px;
    padding-right: 300px;
    color: white;
    z-index: 2000;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
}

/* Hide other content when scores section is active */
body:has(.scores-section:not(.hidden)) .main-container,
body:has(.scores-section:not(.hidden)) header {
    display: none !important;
}

.scores-section.hidden {
    display: none !important;
}

/* Ensure scores section is on top */
.scores-section:not(.hidden) {
    display: flex;
}

.scores-section .section-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    width: 100%;
    max-width: 1000px;
    box-sizing: border-box;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.scores-section h2 {
    margin: 0;
    color: white;
    font-size: 2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.scores-section .section-header .btn {
    background: linear-gradient(135deg, #6c757d, #495057);
    border: none;
    color: white;
    padding: 12px 20px;
    font-size: 0.9rem;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.scores-section .section-header .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(108, 117, 125, 0.4);
    background: linear-gradient(135deg, #5a6268, #3d4043);
}

.scores-main-content {
    width: 100%;
    max-width: 1000px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 50px;
}

.scores-leaderboard {
    margin-bottom: 40px;
    position: relative;
    min-height: 400px;
    width: 100%;
}

.leaderboard-item {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    margin-bottom: 15px;
    padding: 20px;
    position: relative;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    height: 80px;
    overflow: visible;
}

.leaderboard-item.moving {
    position: absolute;
    width: calc(100% - 40px);
    z-index: 1000;
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.25);
}

.leaderboard-item.placeholder {
    opacity: 0.3;
    pointer-events: none;
}

.leaderboard-position {
    flex: 0 0 60px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 900;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin-right: 20px;
}

.leaderboard-position.first {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.leaderboard-position.second {
    background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
    box-shadow: 0 0 20px rgba(192, 192, 192, 0.5);
}

.leaderboard-position.third {
    background: linear-gradient(135deg, #CD7F32, #B8860B);
    box-shadow: 0 0 20px rgba(205, 127, 50, 0.5);
}

.leaderboard-position.other {
    background: linear-gradient(135deg, #6c757d, #495057);
    box-shadow: 0 0 15px rgba(108, 117, 125, 0.3);
}

/* New layout: Position → Team Name → Progress Bar → Points → Profile Pictures */
.team-name-section {
    flex: 0 0 200px;
    margin-right: 20px;
}

.team-name-score {
    font-size: 1.4rem;
    font-weight: 700;
    color: white;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.progress-section {
    flex: 1;
    margin-right: 20px;
}

.progress-bar-container {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    height: 20px;
    width: 100%;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049, #66BB6A);
    border-radius: 15px;
    transition: width 2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 2px;
    animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.score-section {
    flex: 0 0 120px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.current-score {
    font-size: 2.2rem;
    font-weight: 900;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    margin: 0;
    line-height: 1;
}

.score-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
    margin-top: 2px;
}

.team-members-section {
    flex: 0 0 auto;
    display: flex;
    gap: 8px;
    align-items: center;
}

.member-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.6);
    object-fit: cover;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.member-avatar:hover {
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.9);
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progressShine 1s ease-in-out infinite;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.2) 20%,
                rgba(255, 255, 255, 0.1) 40%,
                rgba(255, 255, 255, 0.2) 60%,
                rgba(255, 255, 255, 0.1) 80%,
                rgba(255, 255, 255, 0.2) 100%);
    background-size: 200% 100%;
    animation: progressGlow 3s linear infinite;
    opacity: 0.5;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes progressGlow {
    0% { background-position: 100% 0%; }
    100% { background-position: -100% 0%; }
}

.scores-controls {
    text-align: center;
    margin: 40px 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
}

.back-to-menu-btn {
    background: linear-gradient(135deg, #6c757d, #495057);
    border: none;
    color: white;
    padding: 12px 30px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 15px rgba(108, 117, 125, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.back-to-menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.4);
    background: linear-gradient(135deg, #5a6268, #3d4043);
}

.back-to-menu-btn:active {
    transform: translateY(0);
}

.update-btn {
    background: linear-gradient(135deg, #FF6B6B, #FF8E53);
    border: none;
    color: white;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.update-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(255, 107, 107, 0.4);
}

.update-btn:active {
    transform: translateY(-1px);
}

.update-btn.updating {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    animation: pulse 1.5s infinite;
}

.update-btn.updating i {
    animation: spin 1s linear infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3); }
    50% { box-shadow: 0 12px 30px rgba(76, 175, 80, 0.6); }
    100% { box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3); }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive Design for Scores */
@media (max-width: 768px) {
    .scores-section {
        padding: 15px;
    }

    .scores-section h2 {
        font-size: 1.5rem;
    }

    .scores-section .section-header {
        padding: 15px;
        justify-content: center;
    }

    .scores-section .section-header .btn {
        padding: 10px 15px;
        font-size: 0.8rem;
        right: 15px;
    }

    .leaderboard-item {
        flex-direction: column;
        height: auto;
        padding: 15px;
        gap: 15px;
    }

    .leaderboard-position {
        flex: none;
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-right: 0;
        align-self: center;
    }

    .team-name-section {
        flex: none;
        text-align: center;
        margin-right: 0;
    }

    .team-name-score {
        font-size: 1.2rem;
    }

    .progress-section {
        flex: none;
        margin-right: 0;
        width: 100%;
    }

    .score-section {
        flex: none;
        margin-right: 0;
    }

    .current-score {
        font-size: 1.8rem;
    }

    .team-members-section {
        justify-content: center;
    }

    .member-avatar {
        width: 35px;
        height: 35px;
    }

    .update-btn {
        padding: 12px 30px;
        font-size: 1rem;
    }
}

.scores-summary {
    margin-top: 40px;
    margin-bottom: 50px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    max-width: 1000px;
    width: 100%;
    box-sizing: border-box;
}

.scores-summary h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: white;
    text-align: center;
    font-size: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.round-status {
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 10px;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.round-completed {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.round-pending {
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
    box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
}

.round-status:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

/* Results Section */
.results-section {
    flex: 1;
}

.results-section h2 {
    margin-bottom: 20px;
    color: #1a2a6c;
}

.results-controls {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.results-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.results-container {
    background-color: #f9f9f9;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.result-item:last-child {
    border-bottom: none;
}

.result-team {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1a2a6c;
}

.result-score {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a2a6c;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: white;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    color: #1a2a6c;
}

.close-btn {
    font-size: 1.8rem;
    cursor: pointer;
    color: #777;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
}

/* Settings Tabs */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    color: #777;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #1a2a6c;
}

.tab-btn.active {
    color: #1a2a6c;
    border-bottom: 3px solid #1a2a6c;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Rounds List */
.rounds-list {
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.round-item {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
}

.round-item h3 {
    margin-bottom: 10px;
    color: #1a2a6c;
    padding-right: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.question-count {
    font-size: 0.8em;
    color: #6c757d;
    font-weight: normal;
}

.round-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 10px;
}

.edit-round-btn, .delete-round-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    color: #777;
    transition: color 0.3s ease;
}

.edit-round-btn:hover {
    color: #1a2a6c;
}

.delete-round-btn:hover {
    color: #dc3545;
}

.round-selector {
    margin-bottom: 20px;
}

.round-selector select {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ced4da;
}

.current-round {
    background-color: #e9ecef;
    padding: 8px 15px;
    border-radius: 4px;
    font-weight: bold;
    color: #1a2a6c;
    text-align: left;
    font-size: 1.5rem;
    display: inline-block;
    width: auto;
}

.current-round span {
    font-weight: normal;
    color: #6c757d;
}

/* Questions List */
.questions-list {
    margin-bottom: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.question-item {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
}

.question-item h3 {
    margin-bottom: 10px;
    color: #1a2a6c;
    padding-right: 30px;
}

.question-options {
    list-style: none;
    margin-bottom: 10px;
}

.question-options li {
    padding: 5px 0;
    display: flex;
    align-items: center;
}

.question-options li.correct {
    color: #28a745;
    font-weight: 600;
}

.question-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 10px;
}

.edit-question-btn, .delete-question-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    color: #777;
    transition: color 0.3s ease;
}

.edit-question-btn:hover {
    color: #1a2a6c;
}

.delete-question-btn:hover {
    color: #dc3545;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.image-selection-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.image-preview {
    width: 200px;
    height: 150px;
    border: 1px solid #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
    overflow: hidden;
}

.image-preview img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.question-image {
    max-width: 100%;
    max-height: 100px;
    margin: 10px auto 20px;
    display: block;
    border-radius: 8px;
    justify-content: left;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.question-image:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.question-image-container {
    position: relative;
    text-align: center;
    margin: 15px 0;
}

.image-preview-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: background-color 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.image-preview-button:hover {
    background: #0056b3;
}

.image-preview-button i {
    font-size: 12px;
}

/* Image Preview Modal Styles */
.image-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    align-items: center;
    justify-content: center;
}

.image-preview-modal.active {
    display: flex;
}

.image-preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.image-preview-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: imageModalFadeIn 0.3s ease-out;
}

.image-preview-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: background-color 0.2s ease;
    z-index: 1;
}

.image-preview-close-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

.image-preview-container {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 80vw;
    max-height: 80vh;
}

.image-preview-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

@keyframes imageModalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

input[type="text"], input[type="number"], textarea, select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus, input[type="number"]:focus, textarea:focus {
    border-color: #1a2a6c;
    outline: none;
}

.option-input {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.option-input input[type="radio"] {
    margin-right: 10px;
}

.setting-item {
    margin-bottom: 15px;
}

/* Participant Input */
.participant-input {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.participant-name-container {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 5px;
    min-width: 200px;
}

.participant-name-select,
.participant-name-input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.participant-name-input {
    min-width: 150px;
}

.add-name-btn,
.cancel-name-btn {
    padding: 6px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    min-width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.add-name-btn {
    background-color: #28a745;
    color: white;
}

.add-name-btn:hover {
    background-color: #218838;
}

.cancel-name-btn {
    background-color: #6c757d;
    color: white;
}

.cancel-name-btn:hover {
    background-color: #5a6268;
}

.participant-profile-image {
    flex: 0 0 auto;
    min-width: 120px;
    max-width: 300px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.remove-participant-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #dc3545;
    font-size: 1rem;
    padding: 5px;
    border-radius: 4px;
    min-width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease;
}

.remove-participant-btn:hover {
    color: #bd2130;
}

/* Celebration Animation */
.celebration-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    overflow: hidden;
}

.celebration-popup {
    background-color: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90%;
    width: 400px;
    animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    z-index: 2001;
}

.celebration-popup h2 {
    color: #1a2a6c;
    margin-top: 0;
    font-size: 2rem;
}

.celebration-popup p {
    font-size: 1.4rem;
    margin: 15px 0;
    color: #006633;
    font-weight: 700;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

.celebration-confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #ffcc00;
    opacity: 0.8;
    animation: confettiFall 3s linear forwards;
    transform: rotate(0deg);
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(360deg);
        opacity: 0;
    }
}

@keyframes popIn {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Utility Classes */
.hidden {
    display: none;
}

/* Anagram Styles */
.anagram-container {
    background: linear-gradient(135deg, var(--light-color), #e6e6e6);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    clear: both;
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
}

.anagram-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    z-index: -1;
}

.anagram-container h2 {
    margin-bottom: 25px;
    color: #000000;
    font-size: 1.8rem;
    line-height: 1.5;
    text-align: center;
    font-weight: 700;
    position: relative;
    letter-spacing: 0.5px;
}

.anagram-word-display {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.anagram-letter-box {
    width: 50px;
    height: 50px;
    border: 3px solid #2e3192;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: bold;
    background: linear-gradient(135deg, #ffffff, #f5f5f5);
    color: #2e3192;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.anagram-letter-box.filled {
    background: linear-gradient(135deg, #2e3192, #1a2a6c);
    color: white;
    border-color: #1a2a6c;
    transform: scale(1.05);
}

.anagram-letter-box.hint {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b6914;
    border-color: #ffd700;
}

.anagram-letters-section {
    text-align: center;
    margin-top: 30px;
}

.anagram-letters-section h3 {
    margin-bottom: 20px;
    color: #2e3192;
    font-size: 1.4rem;
    font-weight: 600;
}

.anagram-random-letters {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.anagram-random-letter {
    width: 60px;
    height: 60px;
    border: 3px solid #00a651;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    background: linear-gradient(135deg, #ffffff, #f0f8f0);
    color: #00a651;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    user-select: none;
}

.anagram-random-letter:hover {
    transform: translateY(-3px) scale(1.05);
    border-color: #008a43;
    box-shadow: 0 6px 12px rgba(0, 166, 81, 0.3);
}

.anagram-random-letter.clicked {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-color: #28a745;
    transform: scale(0.9);
    opacity: 0.6;
    cursor: not-allowed;
}

.anagram-random-letter.correct-click {
    animation: correctLetterClick 0.6s ease;
}

.anagram-random-letter.incorrect-click {
    animation: incorrectLetterClick 0.6s ease;
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border-color: #e63946;
    color: #721c24;
}

@keyframes correctLetterClick {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); background: linear-gradient(135deg, #d1ecf1, #bee5eb); }
    100% { transform: scale(0.9); }
}

@keyframes incorrectLetterClick {
    0% { transform: scale(1); }
    25% { transform: translateX(-5px); }
    50% { transform: translateX(5px); }
    75% { transform: translateX(-5px); }
    100% { transform: translateX(0); }
}

.anagram-letter-move {
    animation: letterMove 0.8s ease-in-out;
}

@keyframes letterMove {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.3) translateY(-20px);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.anagram-counter {
    font-size: 1.2rem;
    color: #2e3192;
    font-weight: 600;
}

/* Hint Letters Input Styling */
.hint-letters-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
}

.hint-letter-input {
    display: flex;
    align-items: center;
    gap: 10px;
}

.hint-position, .hint-letter {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
}

.hint-position {
    width: 80px;
}

.hint-letter {
    width: 60px;
    text-align: center;
}

.remove-hint-btn {
    background: #e63946;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.remove-hint-btn:hover {
    background: #dc3545;
}

/* Anagram Details Styling */
.anagram-details {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid #2e3192;
}

.anagram-details p {
    margin: 8px 0;
    font-size: 0.95rem;
    color: #495057;
}

.anagram-details strong {
    color: #2e3192;
    font-weight: 600;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .anagram-letter-box {
        width: 40px;
        height: 40px;
        font-size: 1.4rem;
    }

    .anagram-random-letter {
        width: 50px;
        height: 50px;
        font-size: 1.6rem;
    }

    .anagram-word-display {
        gap: 6px;
    }

    .anagram-random-letters {
        gap: 12px;
    }
}

@media (max-width: 1200px) {
    .options-container {
        grid-template-columns: 1fr;
    }

    .team-card {
        width: calc(33.33% - 15px);
    }
}

@media (max-width: 900px) {
    header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .team-card {
        width: calc(50% - 15px);
    }

    .quiz-header {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 600px) {
    .app-container {
        padding: 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    .team-card {
        width: 100%;
    }

    .nav-buttons {
        flex-direction: column;
        width: 100%;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}