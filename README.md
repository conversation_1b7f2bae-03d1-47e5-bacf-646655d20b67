# Bible Quiz Competition App

A comprehensive Bible quiz application with team management, multiple round types, scoring system, and interactive features.

## 🚀 Quick Start

### Option 1: Using the Batch File (Recommended for Windows)
1. Double-click `run_app.bat`
2. The app will automatically open in your browser
3. Keep the command window open while using the app

### Option 2: Using PowerShell
1. Right-click `run_app.ps1` and select "Run with PowerShell"
2. If prompted about execution policy, type `Y` to allow
3. The app will automatically open in your browser

### Option 3: Manual Setup
1. Install Python (recommended) or Node.js
2. Open command prompt in the app folder
3. Run one of these commands:
   - **Python**: `python -m http.server 8000`
   - **Node.js**: `npx http-server -p 8000`
4. Open your browser and go to `http://localhost:8000`

## ✨ Features

### 🏆 Team Management
- Create and manage multiple teams
- Add team members with profile pictures
- Customizable team card backgrounds
- Team introduction modals

### 📝 Quiz Rounds
- **Question Rounds**: Traditional multiple-choice questions
- **Anagram Rounds**: Interactive letter-clicking puzzles with hints
- Customizable round types and questions
- Image support for questions

### 🎯 Scoring System
- Real-time score tracking
- Animated score updates
- Position change indicators
- Manual score update controls

### 🎵 Audio & Visual Effects
- Background music during anagram rounds
- Sound effects for correct/incorrect answers
- Button hover sounds
- Timer audio alerts
- Smooth animations and transitions

### ⚙️ Settings & Customization
- Adjustable timer duration
- Customizable text sizes and alignment
- Question and team management
- Import/export functionality

## 🎮 How to Use

1. **Setup Teams**: Click "Teams" to create and manage your quiz teams
2. **Add Questions**: Use "Settings" to add questions and anagram puzzles
3. **Start Quiz**: Click the floating action button and select "Start Quiz"
4. **Select Round**: Choose between Question Rounds or Anagram Rounds
5. **Play**: Use the timer, answer questions, and track scores
6. **View Results**: Check the leaderboard and round summaries

## 🔧 Troubleshooting

### Buttons Not Working?
- Make sure you're running the app through a local server (use the batch file)
- Don't open `index.html` directly in the browser
- Check that JavaScript is enabled in your browser

### Audio Not Playing?
- Ensure audio files are in the `audio/` folder
- Check browser audio permissions
- Try refreshing the page

### Images Not Loading?
- Verify image files are in the `images/` and `profile_images/` folders
- Check file names match exactly (case-sensitive)
- Ensure you're using a local server

## 📁 File Structure

```
Bible Quiz Competition/
├── index.html              # Main application file
├── script.js               # Application logic
├── styles.css              # Styling
├── run_app.bat             # Windows launcher
├── run_app.ps1             # PowerShell launcher
├── audio/                  # Sound effects and music
├── images/                 # Question images
├── profile_images/         # Team member photos
└── README.md               # This file
```

## 🎯 Tips for Best Experience

1. **Use Chrome or Firefox** for best compatibility
2. **Keep the server window open** while using the app
3. **Use headphones or speakers** to enjoy audio effects
4. **Create teams first** before starting a quiz
5. **Test audio** by hovering over buttons

## 🆘 Need Help?

If you encounter any issues:
1. Make sure you're using a local server (run the batch file)
2. Check that all files are in the correct folders
3. Try refreshing the browser page
4. Restart the server and try again

Enjoy your Bible Quiz Competition! 🎉
