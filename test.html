<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Introduction Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .test-card:hover {
            transform: translateY(-5px);
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
</head>
<body>
    <h1>Team Introduction Feature Test</h1>
    
    <div class="status info">
        <strong>Test Status:</strong> <span id="testStatus">Initializing...</span>
    </div>
    
    <div class="test-card" onclick="testConfetti()">
        <h3>🎉 Test Confetti Animation</h3>
        <p>Click to test the confetti effect</p>
    </div>
    
    <div class="test-card" onclick="testModal()">
        <h3>📋 Test Modal Structure</h3>
        <p>Click to test modal opening/closing</p>
    </div>
    
    <div class="test-card" onclick="testAnimations()">
        <h3>✨ Test CSS Animations</h3>
        <p>Click to test animation classes</p>
    </div>
    
    <div class="test-card" onclick="openMainApp()">
        <h3>🚀 Open Main Application</h3>
        <p>Click to open the main Bible Quiz app</p>
    </div>

    <script>
        // Test confetti functionality
        function testConfetti() {
            if (typeof confetti === 'undefined') {
                updateStatus('Confetti library not loaded!', 'error');
                return;
            }
            
            updateStatus('Testing confetti...', 'info');
            
            // Trigger confetti
            confetti({
                particleCount: 100,
                spread: 70,
                origin: { y: 0.6 }
            });
            
            setTimeout(() => {
                updateStatus('Confetti test completed successfully!', 'success');
            }, 1000);
        }
        
        // Test modal structure
        function testModal() {
            updateStatus('Testing modal structure...', 'info');
            
            // Create a test modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;
            
            modal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h2>Test Modal</h2>
                    <p>Modal structure is working!</p>
                    <button onclick="this.closest('div[style*=\"position: fixed\"]').remove(); updateStatus('Modal test completed!', 'success');">Close</button>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        // Test animations
        function testAnimations() {
            updateStatus('Testing animations...', 'info');
            
            const testDiv = document.createElement('div');
            testDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0.5);
                background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
                color: white;
                padding: 20px;
                border-radius: 10px;
                font-size: 24px;
                font-weight: bold;
                z-index: 1001;
                animation: testAnimation 2s ease-out forwards;
            `;
            
            // Add animation keyframes
            const style = document.createElement('style');
            style.textContent = `
                @keyframes testAnimation {
                    0% { transform: translate(-50%, -50%) scale(0.5) rotateY(-90deg); opacity: 0; }
                    50% { transform: translate(-50%, -50%) scale(1.1) rotateY(0deg); opacity: 0.8; }
                    100% { transform: translate(-50%, -50%) scale(1) rotateY(0deg); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
            
            testDiv.textContent = 'Animation Test!';
            document.body.appendChild(testDiv);
            
            setTimeout(() => {
                testDiv.remove();
                style.remove();
                updateStatus('Animation test completed!', 'success');
            }, 3000);
        }
        
        // Open main application
        function openMainApp() {
            window.open('index.html', '_blank');
        }
        
        // Update status display
        function updateStatus(message, type) {
            const statusEl = document.getElementById('testStatus');
            const statusContainer = statusEl.parentElement;
            
            statusEl.textContent = message;
            statusContainer.className = `status ${type}`;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('All tests ready. Click on test cards to run individual tests.', 'success');
        });
    </script>
</body>
</html>
