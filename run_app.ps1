# Bible Quiz Competition - PowerShell Launcher
Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Green
Write-Host "║                 BIBLE QUIZ COMPETITION                       ║" -ForegroundColor Green
Write-Host "║                    Local Server Launcher                     ║" -ForegroundColor Green
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Green
Write-Host ""
Write-Host "Starting local web server for the Bible Quiz Competition app..." -ForegroundColor Yellow
Write-Host ""
Write-Host "This will:" -ForegroundColor Cyan
Write-Host "1. Start a local HTTP server on port 8000" -ForegroundColor White
Write-Host "2. Automatically open the app in your default browser" -ForegroundColor White
Write-Host "3. Allow all features to work properly (audio, animations, etc.)" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  IMPORTANT: Keep this window open while using the app!" -ForegroundColor Red
Write-Host "⚠️  Press Ctrl+C to stop the server when you're done." -ForegroundColor Red
Write-Host ""

# Check if Python is available
try {
    $pythonVersion = python --version 2>$null
    if ($pythonVersion) {
        Write-Host "✓ Python found - Starting Python HTTP server..." -ForegroundColor Green
        Write-Host "✓ App will be available at: http://localhost:8000" -ForegroundColor Green
        Write-Host ""
        Write-Host "🌐 Opening browser in 3 seconds..." -ForegroundColor Yellow
        Start-Sleep -Seconds 3
        Start-Process "http://localhost:8000"
        Write-Host ""
        Write-Host "📡 Server is running... (Press Ctrl+C to stop)" -ForegroundColor Cyan
        Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Gray
        python -m http.server 8000
    }
} catch {
    # Check if Node.js is available
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Host "✓ Node.js found - Starting Node.js HTTP server..." -ForegroundColor Green
            Write-Host "✓ App will be available at: http://localhost:8000" -ForegroundColor Green
            Write-Host ""
            Write-Host "🌐 Opening browser in 3 seconds..." -ForegroundColor Yellow
            Start-Sleep -Seconds 3
            Start-Process "http://localhost:8000"
            Write-Host ""
            Write-Host "📡 Server is running... (Press Ctrl+C to stop)" -ForegroundColor Cyan
            Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Gray
            npx http-server -p 8000 -c-1
        }
    } catch {
        Write-Host "❌ Neither Python nor Node.js found." -ForegroundColor Red
        Write-Host "📁 Opening file directly in browser..." -ForegroundColor Yellow
        Write-Host "⚠️  Note: Some features may not work when opening files directly." -ForegroundColor Red
        Write-Host ""
        Start-Sleep -Seconds 2
        Start-Process "index.html"
        Write-Host ""
        Write-Host "💡 For best experience, install Python or Node.js and run this script again." -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Server stopped. Press any key to exit..." -ForegroundColor Gray
Read-Host
